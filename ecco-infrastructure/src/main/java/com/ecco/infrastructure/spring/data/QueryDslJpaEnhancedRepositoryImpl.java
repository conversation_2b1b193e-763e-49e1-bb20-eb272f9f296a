package com.ecco.infrastructure.spring.data;

import com.querydsl.core.types.*;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import org.jspecify.annotations.Nullable;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.Querydsl;
import org.springframework.data.jpa.repository.support.QuerydslJpaRepository;
import org.springframework.data.querydsl.EntityPathResolver;
import org.springframework.data.querydsl.QPageRequest;
import org.springframework.data.querydsl.QSort;
import org.springframework.data.querydsl.SimpleEntityPathResolver;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;


/**
 * @see <a href="http://stackoverflow.com/questions/18300465/spring-data-jpa-and-querydsl-to-fetch-subset-of-columns-using-bean-constructor-p">Stack Overflow</a>
 */
@SuppressWarnings("unchecked")
public class QueryDslJpaEnhancedRepositoryImpl<T, ID extends Serializable> extends QuerydslJpaRepository<T, ID>
        implements QueryDslPredicateAndProjectionExecutor<T, ID> {

    //All instance variables are available in super, but they are private
    private static final EntityPathResolver DEFAULT_ENTITY_PATH_RESOLVER = SimpleEntityPathResolver.INSTANCE;

    static public <T> Slice<T> queryAsSlice(JPQLQuery<T> query, @Nullable QPageRequest pr, ConstructorExpression<T> resultObj) {

        if (pr == null) {
            return new SliceImpl<>(query.select(resultObj).fetch());
        }

        Assert.isTrue(pr.getSort().equals(QSort.unsorted()), "Sort is not supported here");

        int pageSize = pr.getPageSize();
        query = query
                .offset(pr.getOffset())
                .limit(pageSize + 1);

        List<T> resultList = query.select(resultObj).fetch();
        boolean hasNext = resultList.size() > pageSize;

        return new SliceImpl<>(hasNext ? resultList.subList(0, pageSize) : resultList, pr, hasNext);
    }


    private final EntityPath<T> path;
    private final PathBuilder<T> builder;
    private final Querydsl querydsl;

    private final JpaEntityInformation<T, ID> entityInformation;

    private final EntityManager entityManager;

    public QueryDslJpaEnhancedRepositoryImpl(JpaEntityInformation<T, ID> entityInformation, EntityManager entityManager) {
        this(entityInformation, entityManager, DEFAULT_ENTITY_PATH_RESOLVER);
    }

    public QueryDslJpaEnhancedRepositoryImpl(JpaEntityInformation<T, ID> entityInformation, EntityManager entityManager,
                                 EntityPathResolver resolver) {

        super(entityInformation, entityManager, resolver);
        this.path = resolver.createPath(entityInformation.getJavaType());
        this.builder = new PathBuilder<>(path.getType(), path.getMetadata());
        this.querydsl = new Querydsl(entityManager, builder);
        this.entityInformation = entityInformation;
        this.entityManager = entityManager;
    }

    @Override
    public <PROJ> List<PROJ> findAllWithProjection(FactoryExpression<PROJ> factoryExpression, Predicate predicate) {

        JPQLQuery query = createQuery(predicate);
        return query.select(factoryExpression).fetch();
    }

    @Override
    public <PROJ> Page<PROJ> findAllWithProjection(FactoryExpression<PROJ> factoryExpression, Predicate predicate, Pageable pageable) {
        JPQLQuery countQuery = createQuery(predicate);
        JPQLQuery query = querydsl.applyPagination(pageable, createQuery(predicate));

        Long total = countQuery.fetchCount();
        List<PROJ> content = total > pageable.getOffset() ? query.select(factoryExpression).fetch() : Collections.emptyList();

        return new PageImpl<>(content, pageable, total);
    }

    @Override
    public <PROJ> Page<PROJ> findAllWithProjection(FactoryExpression<PROJ> factoryExpression, Predicate predicate,
            Pageable pageable, Expression<?>... groupBy) {

        JPQLQuery countQuery = createQuery(predicate).groupBy(groupBy);
        JPQLQuery query = querydsl.applyPagination(pageable, createQuery(predicate));

        Long total = countQuery.fetchCount();
        List<PROJ> content = total > pageable.getOffset() ? query.select(factoryExpression).fetch() : Collections.emptyList();

        return new PageImpl<>(content, pageable, total);
    }



    @Override
    public <PROJ, T> List<PROJ> findAllWithProjection(FactoryExpression<PROJ> factoryExpression, Predicate predicate,
                                                      QueryModifier<T> queryModifier) {

        JPQLQuery query = createQuery(predicate);
        query = queryModifier.apply(query);
        return query.select(factoryExpression).fetch();
    }

    @Override
    public <PROJ, T> Page<PROJ> findAllWithProjection(FactoryExpression<PROJ> factoryExpression, Predicate predicate,
            Pageable pageable, QueryModifier<T> queryModifier) {

        // Ignoring pageable for now
//        JPQLQuery countQuery = createQuery(predicate);
//        countQuery = queryModifier.apply(countQuery);

        JPQLQuery<T> query = (JPQLQuery<T>) createQuery(predicate);
        query = queryModifier.apply(query);
//        query = querydsl.applyPagination(pageable, query); // paging of the grouped result

//        Long total = countQuery.count();
//        List<PROJ> content = total > pageable.getOffset() ? query.list(factoryExpression) : Collections.<PROJ>emptyList();
//        return new PageImpl<PROJ>(content, pageable, total);

        List<PROJ> content = query.select(factoryExpression).fetch();

        return new PageImpl<>(content, pageable, content.size());
    }

    @Override
    public <PROJ> PROJ findOneWithProjection(FactoryExpression<PROJ> factoryExpression, Predicate predicate) {

        JPAQuery<PROJ> query = (JPAQuery<PROJ>) createQuery(predicate);
        return query.select(factoryExpression).fetchOne();
    }
}