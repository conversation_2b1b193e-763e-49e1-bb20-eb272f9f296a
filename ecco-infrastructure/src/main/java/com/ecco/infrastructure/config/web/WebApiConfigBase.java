package com.ecco.infrastructure.config.web;

import com.ecco.infrastructure.rest.hateoas.schema.EnumSchemaCreator;
import com.ecco.infrastructure.rest.hateoas.schema.ResourceSchemaCreator;
import com.ecco.infrastructure.rest.hateoas.schema.SecurityChecker;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.module.jsonSchema.factories.JsonSchemaFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.orm.jpa.support.OpenEntityManagerInViewInterceptor;
import org.springframework.security.access.expression.method.MethodSecurityExpressionHandler;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.AsyncHandlerInterceptor;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.MappedInterceptor;

import org.jspecify.annotations.NonNull;
import javax.persistence.EntityManagerFactory;
import javax.persistence.PersistenceUnit;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Base Spring configuration for Web API modules.
 * <p/>
 * Extend this class to create a Spring configuration for your Web API module.
 */
@Configuration
@EnableTransactionManagement
@EnableAspectJAutoProxy(proxyTargetClass=true) // for @Aspect. Must match proxying used on @EnableTransactionManagement
@Import({MoreConverters.class, DateFormatterConfig.class})
// NB @EnableWebMvc is required at the top level servlet or Spring Boot app
public abstract class WebApiConfigBase implements WebMvcConfigurer {

    @PersistenceUnit
    private EntityManagerFactory emf;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // FIXME: Doesn't take effect - probably because applicationContext-security.xml has config that overwrites this
        //   so we'd need to replace .xml with config here. Note that this could be hoisted to WebApiConfigBase
        //   so that CalendarWebApiConfig gets this
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowCredentials(true)
                .allowedHeaders("X-Requested-With", "Origin", "Content-Type", "Accept", "Authorization")
                .allowedMethods("GET", "POST", "DELETE", "PATCH");
    }


    @Bean
    public ObjectMapper objectMapper() {
        return ConvertersConfig.getObjectMapper();
    }

    @Override
    public void configureMessageConverters(@NonNull List<HttpMessageConverter<?>> converters) {
        ConvertersConfig.addWebApiConvertersTo(converters);
    }

    @Bean
    SecurityChecker securityChecker(MethodSecurityExpressionHandler methodSecurityExpressionHandler) {
        return new SecurityChecker(methodSecurityExpressionHandler);
    }

    @Bean
    ResourceSchemaCreator schemaCreator(SecurityChecker securityChecker, ObjectMapper objectMapper, EnumSchemaCreator enumSchemaCreator, JsonSchemaFactory schemaFactory) {
        return new ResourceSchemaCreator(securityChecker, objectMapper, enumSchemaCreator, schemaFactory);
    }

    @SuppressWarnings("unused") // We use schemaFactory to indicate a dependency
    @Bean
    EnumSchemaCreator enumSchemaCreator(final JsonSchemaFactory schemaFactory) {
        return new EnumSchemaCreator();
    }

    @Bean
    JsonSchemaFactory jsonSchemaFactory() {
        return new JsonSchemaFactory();
    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        HandlerInterceptor interceptor = new AsyncHandlerInterceptor() {
            @Override
            public void afterCompletion(HttpServletRequest request, @NonNull HttpServletResponse response,
                                        @NonNull Object handler, Exception ex) {
                String param = request.getParameter("cachePeriodSecs");
                if (param != null) {
                    // parse to avoid injection risk rather than just appending string - and satisfies RFC2047
                    int numSeconds = Integer.parseInt(param);
                    response.setHeader("Cache-Control", "max-age=" + numSeconds);
                }
            }
        };

        // Don't apply OEMIVI to some paths where a large session could build up without benefit
        String[] excludePaths = new String[]{"/rota/**"}; // TODO: This will need to change to /api/rota/** for Spring Boot
        var excludePathsInterceptor = new MappedInterceptor(null, excludePaths, interceptor);

        registry.addInterceptor(excludePathsInterceptor);
        OpenEntityManagerInViewInterceptor openEMInViewInterceptor = new OpenEntityManagerInViewInterceptor();
        openEMInViewInterceptor.setEntityManagerFactory(emf);
        registry.addWebRequestInterceptor(openEMInViewInterceptor);
    }
}
