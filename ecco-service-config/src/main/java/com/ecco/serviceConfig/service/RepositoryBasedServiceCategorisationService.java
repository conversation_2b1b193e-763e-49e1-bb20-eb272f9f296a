package com.ecco.serviceConfig.service;

import com.ecco.dom.Service;
import com.ecco.dom.Project;
import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.infrastructure.config.root.CacheConfig;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import com.ecco.serviceConfig.repositories.ProjectRepository;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationToViewModel;
import com.ecco.serviceConfig.viewModel.ServiceCategorisationViewModel;
import com.google.common.collect.Lists;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import java.util.List;

@org.springframework.stereotype.Service("serviceCategorisationService")
@ReadOnlyTransaction
public class RepositoryBasedServiceCategorisationService {

    private final ServiceRepository serviceRepository;
    private final ProjectRepository projectRepository;
    private final ServiceCategorisationRepository serviceCategorisationRepository;
    private final ServiceCategorisationToViewModel serviceCategorisationToViewModel;

    @Autowired
    public RepositoryBasedServiceCategorisationService(ServiceCategorisationRepository serviceCategorisationRepository,
                                                       ServiceRepository serviceRepository,
                                                       ProjectRepository projectRepository,
                                                       ServiceCategorisationToViewModel serviceCategorisationToViewModel) {
        super();
        this.serviceCategorisationRepository = serviceCategorisationRepository;
        this.serviceRepository = serviceRepository;
        this.projectRepository = projectRepository;
        this.serviceCategorisationToViewModel = serviceCategorisationToViewModel;
    }

    // ?? what is this for
    public List<ServiceCategorisationViewModel> getServiceCategorisationViewModels(List<ServiceCategorisation> svcCats) {
        // don't call this. as it won't be cached as it will bypass the aop
        return svcCats.stream()
                .map(serviceCategorisationToViewModel)
                .toList();
    }

    @Cacheable(value=CacheConfig.CACHE_SVCCAT, key="'_all_'")
    public List<ServiceCategorisation> getAll() {
        return Lists.newArrayList(serviceCategorisationRepository.findAll());
    }

    @Cacheable(value= CacheConfig.CACHE_SVCCAT)
    public ServiceCategorisation getServiceCategorisation(int svcCatId) {
        return serviceCategorisationRepository.findById(svcCatId).orElseThrow();
    }

    @Cacheable(value=CacheConfig.CACHE_SERVICE, key="'_all_'")
    public List<com.ecco.dom.Service> getServices() {
        return Lists.newArrayList(serviceRepository.findAll());
    }

    @Cacheable(CacheConfig.CACHE_SERVICE)
    public Service getService(Long id) {
        return serviceRepository.findById(id).orElseThrow();
    }

    @Cacheable(value=CacheConfig.CACHE_PROJECT, key="'_all_'")
    public List<Project> getProjects() {
        return Lists.newArrayList(projectRepository.findAll());
    }

    @Cacheable(CacheConfig.CACHE_PROJECT)
    public Project getProject(Long id) {
        return projectRepository.findById(id).orElseThrow();
    }
}
